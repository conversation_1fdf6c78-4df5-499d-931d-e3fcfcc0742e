const logger = require('./logger');

class WebSocketMonitor {
  constructor(id, url, reconnectInterval = 5000, maxReconnectAttempts = 10) {
    this.id = id;
    this.url = url;
    this.reconnectInterval = reconnectInterval;
    this.maxReconnectAttempts = maxReconnectAttempts;
    this.reconnectAttempts = 0;
    this.websocket = null;
    this.isConnected = false;
    this.lastDataReceived = Date.now();
    this.healthCheckInterval = 10000; // 10 seconds
    this.healthCheckTimer = null;
    this.reconnectTimer = null;
    this.handlers = {
      onMessage: null,
      onError: null,
      onClose: null,
      onOpen: null
    };
  }

  setHandlers(handlers) {
    this.handlers = { ...this.handlers, ...handlers };
  }

  connect() {
    if (this.websocket) {
      this.websocket.removeAllListeners();
      this.websocket.close();
    }

    try {
      const WebSocket = require('ws');
      this.websocket = new WebSocket(this.url);
      
      this.websocket.on('open', () => {
        this.onOpen();
      });
      
      this.websocket.on('message', (data) => {
        this.onMessage(data);
      });
      
      this.websocket.on('error', (error) => {
        this.onError(error);
      });
      
      this.websocket.on('close', (code, reason) => {
        this.onClose(code, reason);
      });
    } catch (error) {
      logger.error(`Failed to create WebSocket connection for monitor ${this.id}: ${error.message}`);
      this.scheduleReconnect();
    }
  }

  onOpen() {
    logger.info(`WebSocket connection ${this.id} opened: ${this.url}`);
    this.isConnected = true;
    this.reconnectAttempts = 0;
    this.lastDataReceived = Date.now();
    
    if (this.handlers.onOpen) {
      this.handlers.onOpen();
    }
    
    // Start health check
    this.startHealthCheck();
  }

  onMessage(data) {
    this.lastDataReceived = Date.now();
    
    if (this.handlers.onMessage) {
      this.handlers.onMessage(data);
    }
  }

  onError(error) {
    logger.error(`WebSocket connection ${this.id} error: ${error.message}`);
    
    if (this.handlers.onError) {
      this.handlers.onError(error);
    }
  }

  onClose(code, reason) {
    logger.warn(`WebSocket connection ${this.id} closed. Code: ${code}, Reason: ${reason}`);
    this.isConnected = false;
    
    // Stop health check
    this.stopHealthCheck();
    
    if (this.handlers.onClose) {
      this.handlers.onClose(code, reason);
    }
    
    // Schedule reconnect
    this.scheduleReconnect();
  }

  startHealthCheck() {
    this.stopHealthCheck();
    
    this.healthCheckTimer = setInterval(() => {
      const timeSinceLastData = Date.now() - this.lastDataReceived;
      
      // If no data received for more than 10 seconds, reconnect
      if (timeSinceLastData > 10000) {
        logger.warn(`WebSocket connection ${this.id} timeout detected. Time since last data: ${timeSinceLastData}ms`);
        this.reconnect();
      }
    }, this.healthCheckInterval);
  }

  stopHealthCheck() {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = null;
    }
  }

  scheduleReconnect() {
    const system = require('./index');
    if (system.stopped) {
      logger.info(`Not scheduling reconnect for WebSocket connection ${this.id} because system is stopped.`);
      return;
    }
    // Clear any existing reconnect timer
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    // Check if we've exceeded max reconnect attempts
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error(`Max reconnect attempts reached for WebSocket connection ${this.id}. Giving up.`);
      return;
    }
    
    // Calculate backoff delay (exponential backoff)
    const backoffDelay = Math.min(
      this.reconnectInterval * Math.pow(2, this.reconnectAttempts),
      60000 // Max 60 seconds
    );
    
    this.reconnectAttempts++;
    logger.info(`Scheduling reconnect for WebSocket connection ${this.id} in ${backoffDelay}ms (attempt ${this.reconnectAttempts})`);
    
    this.reconnectTimer = setTimeout(() => {
      this.reconnect();
    }, backoffDelay);
  }

  reconnect() {
    const system = require('./index');
    if (system.stopped) {
      logger.info(`Not reconnecting WebSocket connection ${this.id} because system is stopped.`);
      return;
    }
    logger.info(`Reconnecting WebSocket connection ${this.id}...`);
    this.connect();
  }

  send(data) {
    if (this.websocket && this.isConnected) {
      try {
        this.websocket.send(data);
      } catch (error) {
        logger.error(`Failed to send data on WebSocket connection ${this.id}: ${error.message}`);
      }
    } else {
      logger.warn(`Cannot send data, WebSocket connection ${this.id} is not open`);
    }
  }

  close() {
    logger.info(`Closing WebSocket connection ${this.id}`);
    
    // Stop timers
    this.stopHealthCheck();
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    // Close WebSocket
    if (this.websocket) {
      this.websocket.close();
    }
  }

  getStatus() {
    return {
      id: this.id,
      url: this.url,
      isConnected: this.isConnected,
      lastDataReceived: this.lastDataReceived,
      timeSinceLastData: Date.now() - this.lastDataReceived,
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts
    };
  }
}

module.exports = WebSocketMonitor;
