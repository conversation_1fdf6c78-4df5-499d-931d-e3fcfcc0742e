const WebSocketMonitor = require('./websocket-monitor');
const { tickQueue } = require('./queue');
const logger = require('./logger');
const configService = require('./config-service');
const dotenv = require('dotenv');
// Load environment variables from .env file
dotenv.config();
const debugMode = process.env.DEBUG_MODE === 'true';

class TickCollector {
  constructor() {
    this.monitors = new Map();
    this.symbols = new Map();
    this.distributedConnections = [];
    this.config = null;
  }

  async initializeSymbols() {
    try {
      // Get configuration from service
      this.config = await configService.getConfig();
      const symbolsConfig = configService.getSymbolsConfig();
      const websocketConfig = configService.getWebsocketConfig();

      // Flatten all symbols from all connections
      const allPairs = [];
      symbolsConfig.connections.forEach(connection => {
        connection.pairs.forEach(pair => {
          allPairs.push({
            connectionId: connection.id,
            pair: pair
          });
        });
      });

      // Distribute pairs across connections based on pairsPerConnection limit
      const pairsPerConnection = parseInt(websocketConfig.pairsPerConnection);
      const totalConnectionsNeeded = Math.ceil(allPairs.length / pairsPerConnection);

      // Create distributed connections
      this.distributedConnections = [];
      for (let i = 0; i < totalConnectionsNeeded; i++) {
        const startIndex = i * pairsPerConnection;
        const endIndex = Math.min(startIndex + pairsPerConnection, allPairs.length);
        const connectionPairs = allPairs.slice(startIndex, endIndex);

        this.distributedConnections.push({
          id: i + 1,
          pairs: connectionPairs.map(item => item.pair)
        });

        // Initialize symbols map
        connectionPairs.forEach(item => {
          this.symbols.set(item.pair, {
            connectionId: i + 1,
            pair: item.pair
          });
        });
      }

      logger.info(`Initialized ${this.symbols.size} symbols across ${this.distributedConnections.length} connections (limited to ${pairsPerConnection} pairs per connection)`);
    } catch (error) {
      logger.error(`Failed to initialize symbols: ${error.message}`);
      throw error;
    }
  }

  async start() {
    logger.info('Starting kline collector...');

    try {
      // Initialize symbols first (this will fetch config)
      await this.initializeSymbols();

      const websocketConfig = configService.getWebsocketConfig();

      // Create WebSocket monitors for each distributed connection
      this.distributedConnections.forEach(connection => {
        let klineInterval = websocketConfig.klineInterval;
        if (!['1m', '5m', '15m'].includes(klineInterval)) {
          klineInterval = '1m';
        }
        const wsUrl = `wss://fstream.binance.com/ws/${connection.pairs.map(p => p.toLowerCase() + '@kline_' + klineInterval).join('/')}`;
        const monitor = new WebSocketMonitor(connection.id, wsUrl);

        monitor.setHandlers({
          onOpen: () => {
            logger.info(`Connected to Binance Futures WebSocket for connection ${connection.id} with ${connection.pairs.length} pairs`);
          },
          onMessage: (data) => {
            this.processKlineData(data, connection.id);
          },
          onError: (error) => {
            logger.error(`WebSocket error for connection ${connection.id}: ${error.message}`);
          },
          onClose: (code, reason) => {
            logger.warn(`WebSocket closed for connection ${connection.id}. Code: ${code}, Reason: ${reason}`);
          }
        });

        this.monitors.set(connection.id, monitor);
        monitor.connect();
      });
    } catch (error) {
      logger.error(`Failed to start kline collector: ${error.message}`);
      throw error;
    }
  }

  processKlineData(data, connectionId) {
    try {
      const klineData = JSON.parse(data);
      
      // Validate kline data
      if (!klineData || !klineData.e || klineData.e !== 'kline' || !klineData.k) {
        debugMode && logger.debug('Received invalid kline data');
        return;
      }
      
      // Transform kline data to our format with proper validation
      const kline = klineData.k;
      const processedKline = {
        eventType: 'kline',
        eventTime: parseInt(klineData.E) || 0,
        symbol: (klineData.s || '').toLowerCase(),
        kline: {
          startTime: parseInt(kline.t) || 0,
          endTime: parseInt(kline.T) || 0,
          symbol: (kline.s || '').toLowerCase(),
          interval: (kline.i || ''),
          firstTradeId: parseInt(kline.f) || 0,
          lastTradeId: parseInt(kline.L) || 0,
          openPrice: parseFloat(kline.o) || 0,
          closePrice: parseFloat(kline.c) || 0,
          highPrice: parseFloat(kline.h) || 0,
          lowPrice: parseFloat(kline.l) || 0,
          baseAssetVolume: parseFloat(kline.v) || 0,
          numberOfTrades: parseInt(kline.n) || 0,
          isClosed: Boolean(kline.x),
          quoteAssetVolume: parseFloat(kline.q) || 0,
          takerBuyBaseAssetVolume: parseFloat(kline.V) || 0,
          takerBuyQuoteAssetVolume: parseFloat(kline.Q) || 0
        }
      };
      
      // Add to Redis queue
      this.enqueueKline(processedKline);
    } catch (error) {
      logger.error(`Error processing kline data: ${error.message}`);
    }
  }

  async enqueueKline(kline) {
    try {
      await tickQueue.add('kline', kline, {
        jobId: `${kline.symbol}-${kline.kline.startTime}-${Date.now()}`,
        removeOnComplete: true
      });
      
      debugMode && logger.debug(`Enqueued kline for ${kline.symbol} at ${kline.kline.startTime}`);
    } catch (error) {
      logger.error(`Failed to enqueue kline for ${kline.symbol}: ${error.message}`);
    }
  }

  getStatus() {
    const status = {};
    for (const [id, monitor] of this.monitors) {
      status[id] = monitor.getStatus();
    }
    return status;
  }

  close() {
    logger.info('Closing all WebSocket connections...');
    for (const [id, monitor] of this.monitors) {
      monitor.close();
    }
  }

  getConnectionIdForSymbol(symbol) {
    const symbolData = this.symbols.get(symbol.toUpperCase());
    return symbolData ? symbolData.connectionId : null;
  }
}

module.exports = TickCollector;
