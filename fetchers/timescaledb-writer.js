const { Pool } = require('pg');
const logger = require('./logger');
const configService = require('./config-service');
const dotenv = require('dotenv');

// Load environment variables from .env file
dotenv.config();

const debugMode = process.env.DEBUG_MODE === 'true';

// TimescaleDB Bağlantı Konfigürasyonu
const DB_CONFIG = {
  host: "localhost",
  port: 5432,
  database: "postgres",
  user: "tanersubasi",
  password: ""
};

class TimescaleDBWriter {
  constructor() {
    this.pool = null;
    this.configID = null;
    this.buffers = new Map(); // Buffer for each symbol
    this.flushTimers = new Map(); // Timer for each symbol
    this.flushInterval = 1000; // 1 second
    this.batchSize = 100; // Flush when buffer reaches 100 records
    this.initialized = false;
    this.initializationPromise = null;
    this.klineInterval = '1m'; // Default interval
  }

  async initDatabase() {
    try {
      // Get configID from config service if not already set
      if (!this.configID) {
        let conf = await configService.getConfig();
        console.log('Config loaded:', conf);
        this.klineInterval = conf.websocket.klineInterval || '1m';
        this.configID = configService.getConfigID();
      }

      // Create PostgreSQL connection pool
      this.pool = new Pool(DB_CONFIG);
      
      // Test connection
      const client = await this.pool.connect();
      await client.query('SELECT NOW()');
      client.release();
      
      this.initialized = true;
      logger.info(`TimescaleDB connection pool initialized`);
    } catch (err) {
      logger.error(`Failed to initialize TimescaleDB: ${err.message}`);
      throw err;
    }
  }

  async createTable(symbol) {
    if (!this.pool) {
      logger.error('Database pool not initialized');
      return;
    }

    try {
      const tableName = symbol.toLowerCase();
      const createTableSQL = `
        CREATE TABLE IF NOT EXISTS "${tableName}" (
          id SERIAL PRIMARY KEY,
          event_type TEXT NOT NULL,
          event_time BIGINT NOT NULL,
          symbol TEXT NOT NULL,
          kline_start_time BIGINT NOT NULL,
          kline_end_time BIGINT NOT NULL,
          kline_symbol TEXT NOT NULL,
          kline_interval TEXT NOT NULL,
          kline_first_trade_id BIGINT NOT NULL,
          kline_last_trade_id BIGINT NOT NULL,
          kline_open_price DECIMAL NOT NULL,
          kline_close_price DECIMAL NOT NULL,
          kline_high_price DECIMAL NOT NULL,
          kline_low_price DECIMAL NOT NULL,
          kline_base_asset_volume DECIMAL NOT NULL,
          kline_number_of_trades INTEGER NOT NULL,
          kline_is_closed BOOLEAN NOT NULL,
          kline_quote_asset_volume DECIMAL NOT NULL,
          kline_taker_buy_base_asset_volume DECIMAL NOT NULL,
          kline_taker_buy_quote_asset_volume DECIMAL NOT NULL,
          timestamp TIMESTAMPTZ DEFAULT NOW()
        );
      `;
      
      // Create hypertable for TimescaleDB (time-series optimization)
      const createHypertableSQL = `
        SELECT create_hypertable('${tableName}', 'timestamp', if_not_exists => TRUE);
      `;
      
      const client = await this.pool.connect();
      try {
        await client.query(createTableSQL);
        
        // Try to create hypertable, but don't fail if TimescaleDB extension is not available
        try {
          await client.query(createHypertableSQL);
          debugMode && logger.debug(`Hypertable created/verified for symbol: ${symbol}`);
        } catch (hypertableError) {
          // This is expected if TimescaleDB extension is not installed
          debugMode && logger.debug(`Hypertable creation skipped for ${symbol}: ${hypertableError.message}`);
        }
        
        debugMode && logger.debug(`Table created/verified for symbol: ${symbol}`);
      } finally {
        client.release();
      }
    } catch (err) {
      logger.error(`Failed to create table for symbol ${symbol}: ${err.message}`);
    }
  }

  async ensureInitialized() {
    if (!this.initialized) {
      // If initialization is in progress, wait for it to complete
      if (this.initializationPromise) {
        await this.initializationPromise;
        return;
      }
      
      // Start initialization and store the promise
      this.initializationPromise = this.initDatabase()
        .then(() => {
          this.initializationPromise = null;
        })
        .catch((error) => {
          this.initializationPromise = null;
          throw error;
        });
      
      await this.initializationPromise;
    }
  }

  async bufferTick(symbol, tick) {
    // Ensure database is initialized before buffering ticks
    await this.ensureInitialized();

    if (!this.buffers.has(symbol)) {
      this.buffers.set(symbol, []);
    }

    const buffer = this.buffers.get(symbol);
    buffer.push(tick);

    // Set up flush timer if not already set
    if (!this.flushTimers.has(symbol)) {
      this.flushTimers.set(symbol, setTimeout(() => {
        this.flushSymbol(symbol);
      }, this.flushInterval));
    }

    // Flush immediately if buffer is full
    if (buffer.length >= this.batchSize) {
      this.flushSymbol(symbol);
    }
  }

  flushSymbol(symbol) {
    if (!this.buffers.has(symbol) || this.buffers.get(symbol).length === 0) {
      return;
    }

    const buffer = this.buffers.get(symbol);
    this.writeBatch(symbol, buffer);
    
    // Clear buffer
    buffer.length = 0;
    
    // Clear timer
    if (this.flushTimers.has(symbol)) {
      clearTimeout(this.flushTimers.get(symbol));
      this.flushTimers.delete(symbol);
    }
  }

  async writeBatch(symbol, ticks) {
    if (ticks.length === 0) return;

    try {
      await this.createTable(symbol);

      const tableName = symbol.toLowerCase();

      // Prepare batch data
      const values = [];
      const placeholders = [];
      let paramIndex = 1;

      for (let i = 0; i < ticks.length; i++) {
        const tick = ticks[i];

        // Ensure all values are of correct types with proper validation for kline data
        const eventType = String(tick.eventType || 'kline');
        const eventTime = Number(tick.eventTime) || 0;
        const symbol = String(tick.symbol || '');

        const kline = tick.kline || {};
        const klineStartTime = Number(kline.startTime) || 0;
        const klineEndTime = Number(kline.endTime) || 0;
        const klineSymbol = String(kline.symbol || symbol);
        const klineInterval = String(kline.interval || '1m');
        const klineFirstTradeId = Number(kline.firstTradeId) || 0;
        const klineLastTradeId = Number(kline.lastTradeId) || 0;
        const klineOpenPrice = Number(kline.openPrice) || 0;
        const klineClosePrice = Number(kline.closePrice) || 0;
        const klineHighPrice = Number(kline.highPrice) || 0;
        const klineLowPrice = Number(kline.lowPrice) || 0;
        const klineBaseAssetVolume = Number(kline.baseAssetVolume) || 0;
        const klineNumberOfTrades = Number(kline.numberOfTrades) || 0;
        const klineIsClosed = Boolean(kline.isClosed);
        const klineQuoteAssetVolume = Number(kline.quoteAssetVolume) || 0;
        const klineTakerBuyBaseAssetVolume = Number(kline.takerBuyBaseAssetVolume) || 0;
        const klineTakerBuyQuoteAssetVolume = Number(kline.takerBuyQuoteAssetVolume) || 0;

        // Add values to batch
        values.push(
          eventType, eventTime, symbol, klineStartTime,
          klineEndTime, klineSymbol, klineInterval, klineFirstTradeId,
          klineLastTradeId, klineOpenPrice, klineClosePrice, klineHighPrice,
          klineLowPrice, klineBaseAssetVolume, klineNumberOfTrades, klineIsClosed,
          klineQuoteAssetVolume, klineTakerBuyBaseAssetVolume, klineTakerBuyQuoteAssetVolume
        );

        // Create placeholder for this row
        const rowPlaceholders = [];
        for (let j = 0; j < 19; j++) {
          rowPlaceholders.push(`$${paramIndex++}`);
        }
        placeholders.push(`(${rowPlaceholders.join(', ')})`);
      }

      // Build optimized batch insert SQL
      const insertSQL = `
        INSERT INTO "${tableName}" (
          event_type, event_time, symbol, kline_start_time,
          kline_end_time, kline_symbol, kline_interval, kline_first_trade_id,
          kline_last_trade_id, kline_open_price, kline_close_price, kline_high_price,
          kline_low_price, kline_base_asset_volume, kline_number_of_trades, kline_is_closed,
          kline_quote_asset_volume, kline_taker_buy_base_asset_volume, kline_taker_buy_quote_asset_volume
        ) VALUES ${placeholders.join(', ')}
      `;

      const client = await this.pool.connect();

      try {
        // Begin transaction
        await client.query('BEGIN');

        // Execute single batch insert
        await client.query(insertSQL, values);

        // Commit transaction
        await client.query('COMMIT');

        debugMode && logger.debug(`Batch inserted ${ticks.length} klines for symbol ${symbol}`);
      } catch (err) {
        // Rollback transaction on error
        await client.query('ROLLBACK');
        throw err;
      } finally {
        client.release();
      }
    } catch (err) {
      logger.error(`Failed to write batch for symbol ${symbol}: ${err.message}`);
    }
  }

  flushAll() {
    for (const symbol of this.buffers.keys()) {
      this.flushSymbol(symbol);
    }
  }

  async closeDatabase() {
    if (this.pool) {
      try {
        this.flushAll();
        await this.pool.end();
        logger.info(`TimescaleDB connection pool closed`);
      } catch (err) {
        logger.error(`Error closing database pool: ${err.message}`);
      } finally {
        this.pool = null;
      }
    }
  }
}

module.exports = TimescaleDBWriter;
