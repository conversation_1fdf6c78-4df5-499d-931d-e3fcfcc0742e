const configService = require('./config-service');
const logger = require('./logger');

async function debugConfig() {
  try {
    console.log('Fetching configuration...');
    const config = await configService.getConfig();
    
    console.log('Full configuration:');
    console.log(JSON.stringify(config, null, 2));
    
    console.log('\nSymbols configuration:');
    const symbolsConfig = configService.getSymbolsConfig();
    console.log(JSON.stringify(symbolsConfig, null, 2));
    
    console.log('\nWebsocket configuration:');
    const websocketConfig = configService.getWebsocketConfig();
    console.log(JSON.stringify(websocketConfig, null, 2));
    
    console.log('\nConfig ID:');
    const configID = configService.getConfigID();
    console.log(configID);
  } catch (error) {
    console.error('Error fetching configuration:', error.message);
    console.error(error.stack);
  }
}

debugConfig();
