
Sen profesyonel bir backend AI agentsin. Görevin: Mac Studio M1 Max üzerinde çalışan, Binance Futures piyasasından 100 işlem çiftinin tick verisini WebSocket üzerinden toplayan, Redis queue sistemi ile <PERSON>, çok çekirdekli (10 core) işleme mimarisiyle SQLite'e kayıt eden, kendi bağlantı durumunu izleyip gerektiğinde otomatik restart eden, gözlemlenebilir ve dayanıklı bir sistem oluşturmaktır.

📦 TEKNOLOJİ YIĞINI:
- Node.js (v18+)
- Redis (ioredis / BullMQ)
- SQLite (better-sqlite3 veya sqlite3 modülü)
- Çok çekirdekli kullanım: `worker_threads` veya `cluster`
- Donanım: Mac Studio M1 Max (10 core, 32 GB RAM)

🎯 GÖREVLER:
1. Sistemi aşağıdaki modüllere böl:
tick-collector.js → Binance WebSocket üzerinden tick verisi toplayıp Redis kuyruğuna aktarır

tick-worker.js → Redis kuyruğundan tick verisi alıp SQLite'e yazar (çok çekirdekli işlem)

queue.js → Redis bağlantısı ve ortak queue tanımı

sqlite-writer.js → SQLite işlemleri ve batch insert fonksiyonu

websocket-monitor.js → WebSocket bağlantı durumu izleme, yeniden başlatma (self-healing)

health-server.js → Express.js ile HTTP health endpoint'i ve bağlantı durumu raporu

logger.js → Loglama (günlük bazlı log dosyaları)


2. 100 sembolü 2 WebSocket bağlantısına böl ve her bağlantıyı `websocket-monitor.js` ile izlet:
- Her bağlantı ayrı bir `tick-streamer` olarak izlenir
- 10 saniyeden fazla veri gelmezse otomatik restart edilir
- Yeniden bağlanma limiti / backoff retry algoritması uygulanır

3. Gelen tick verisi Redis’te `tickQueue` kuyruğuna aktarılır

4. `tick-worker.js` her CPU çekirdeği için 1 worker thread başlatır (`os.cpus().length`)
- Her worker Redis’ten veri çeker
- Her sembol için tablo açar (örnek: `btcusdt`)
- Tick verilerini `ticks-YYYY-MM-DD.sqlite` dosyasına batch olarak yazar
- Flush interval: 100 kayıt / 1 saniye (hangisi önce dolarsa)

5. `.jsonl` dosyası tutulmaz, tüm veri doğrudan SQLite’e gider

6. Sistemin **gözlemlenebilirlik ve uyarı özellikleri**:
- Her WebSocket bağlantısının durumu `health-server.js` üzerinden `/status` endpoint'inden izlenebilir
- Redis kuyruk durumu (`waiting`, `active`, `failed`, `completed`) loglanır
- Günlük `logs/YYYY-MM-DD.log` dosyasında tüm hatalar ve reconnect eventleri kayıt altına alınır
- Prometheus uyumlu `/metrics` endpoint'i eklenebilir (opsiyonel)

7. Her gün sonunda SQLite dosyası kapatılır, salt okunur yapılabilir veya S3 gibi bir storage'a gönderilebilir (opsiyonel)

8. Sistem başlatıldığında:
- Redis bağlantısı kurulur
- Her WebSocket bağlantısı başlatılır ve monitor edilir
- Tüm CPU çekirdekleri için worker başlatılır
- Health server devreye girer

🛠 DONANIM GEREKLİLİKLERİ:
- Donanım: Mac Studio M1 Max (10 core CPU, 32 GB RAM)
- SSD disk önerilir (min 256 GB) — günlük yaklaşık 250-500 MB veri yazımı olur
- Redis çalışıyor olmalı (`localhost:6379`)
- Node.js v18+, `pm2` ile yönetilebilir olmalı

💬 AJANIN GÖREVİ:
Aşağıdaki her bileşeni kodla ve çalışır hale getir:
- tick-collector.js
- websocket-monitor.js
- tick-worker.js
- queue.js
- sqlite-writer.js
- health-server.js
- logger.js
- symbols.json (örnek 100 pair listesi)
- package.json ve README.md

Kodlar test edilebilir, extensible ve production-grade olmalı. Hataları kaldır, reconnect ve ölçeklenebilirliği yönet. Yazılım çalıştırıldığında veri toplamaya başlasın ve kendi kendine izleyerek stabil şekilde sürsün.





-----

# 🚀 Binance Tick Collector - Modern Monitoring Dashboard

## 📊 Özellikler

### 🎯 Ana Dashboard Özellikleri
- **Real-time Monitoring**: Canlı sistem durumu ve performans metrikleri
- **Trading Pair Yönetimi**: 116 trading pair'in durumunu izleme ve kontrol etme
- **Sistem Performansı**: CPU, RAM kullanımı ve queue durumu
- **Storage Monitoring**: SQLite veritabanı boyutu ve dosya detayları
- **Activity Log**: Real-time sistem aktivite günlükleri
- **Problem Detection**: Sorunlu pair'leri otomatik tespit etme

### 🔧 Teknik Özellikler
- **Modern UI**: Tailwind CSS ile responsive tasarım
- **Real-time Updates**: Socket.IO ile canlı veri akışı
- **Interactive Charts**: Chart.js ile dinamik grafikler
- **RESTful API**: Comprehensive monitoring endpoints
- **Security**: Helmet.js, CORS, rate limiting
- **Performance**: Compression, caching optimizations

## 🎨 Dashboard Bileşenleri

### 1. System Overview Cards
- **System Status**: Genel sistem durumu (Healthy/Unhealthy)
- **Memory Usage**: RAM kullanım yüzdesi
- **Queue Size**: Bekleyen işlem sayısı
- **Storage Used**: Toplam kullanılan disk alanı

### 2. Performance Charts
- **Memory Usage Over Time**: Zaman içinde bellek kullanımı
- **Queue Statistics**: Queue durumu (waiting, active, failed)

### 3. Trading Pairs Management
- **Pair Status Grid**: Tüm trading pair'lerin durumu
- **Filter & Search**: Pair'leri filtreleme ve arama
- **Individual Controls**: Her pair için start/stop butonları
- **Bulk Operations**: Tüm pair'leri toplu start/stop
- **Status Indicators**: 
  - 🟢 Active: Aktif veri akışı
  - 🟡 Warning: Gecikmeli veri
  - 🔴 Error: Hata durumu
  - ⚫ Stopped: Durdurulmuş

### 4. Problem Pairs Section
- **Automatic Detection**: Sorunlu pair'leri otomatik tespit
- **Error Tracking**: Hata sayısı ve türü
- **Last Update Tracking**: Son veri alma zamanı
- **Quick Actions**: Hızlı müdahale butonları

### 5. System Details
- **Redis Status**: Redis bağlantı durumu ve bilgileri
- **Storage Details**: Dosya listesi ve boyutları
- **Performance Metrics**: CPU load, memory, queue load progress bars

### 6. Activity Log
- **Real-time Logging**: Sistem aktivitelerinin canlı takibi
- **Color Coded**: Farklı log seviyeleri için renk kodları
- **Pause/Resume**: Log akışını durdurma/devam ettirme
- **Clear Function**: Log geçmişini temizleme
- **Auto-scroll**: Yeni log'lar için otomatik kaydırma

## 🌐 API Endpoints

### Monitoring Endpoints
```
GET  /api/health          - Sistem sağlık durumu
GET  /api/status          - Detaylı sistem durumu
GET  /api/pairs           - Trading pair durumları
GET  /api/metrics         - Sistem metrikleri
GET  /api/queue/stats     - Queue istatistikleri
GET  /api/storage         - Storage bilgileri
GET  /metrics             - Prometheus metrikleri
```

### Control Endpoints
```
POST /api/pairs/:pair/start  - Pair'i başlat
POST /api/pairs/:pair/stop   - Pair'i durdur
```

## 🚀 Kullanım

### Dashboard'a Erişim
```bash
# Sistem başlatma
npm start

# Dashboard URL
http://localhost:3000
```

### Pair Yönetimi
1. **Individual Control**: Her pair'in yanındaki ▶️ (start) ve ⏹️ (stop) butonları
2. **Bulk Operations**: "Start All" ve "Stop All" butonları
3. **Filtering**: Pair adı veya duruma göre filtreleme
4. **Search**: Üst kısımdaki arama kutusu

### Monitoring
1. **Real-time Updates**: Dashboard otomatik olarak 5 saniyede bir güncellenir
2. **Manual Refresh**: "Refresh" butonu ile manuel güncelleme
3. **Connection Status**: Header'da bağlantı durumu göstergesi

### Problem Solving
1. **Problem Pairs**: Sağ panelde sorunlu pair'ler otomatik listelenir
2. **Activity Log**: Alt kısımda tüm sistem aktiviteleri görüntülenir
3. **Performance Metrics**: CPU, RAM ve queue yükü progress bar'larında

## 📱 Responsive Design

Dashboard tüm cihazlarda optimize edilmiştir:
- **Desktop**: Full feature set
- **Tablet**: Responsive grid layout
- **Mobile**: Stacked layout, touch-friendly controls

## 🎨 UI/UX Features

### Visual Indicators
- **Status Colors**: Green (healthy), Yellow (warning), Red (error), Gray (unknown)
- **Glow Effects**: Critical status'ler için glow efektleri
- **Animations**: Smooth transitions ve hover effects
- **Progress Bars**: Visual performance indicators

### Interactive Elements
- **Hover Effects**: Card'lar ve butonlar için hover animasyonları
- **Click Feedback**: Button press animasyonları
- **Loading States**: İşlem sırasında loading göstergeleri
- **Notifications**: Toast notifications for actions

### Accessibility
- **Color Contrast**: WCAG uyumlu renk kontrastları
- **Font Sizes**: Okunabilir font boyutları
- **Touch Targets**: Mobile-friendly touch areas
- **Keyboard Navigation**: Keyboard accessibility

## 🔧 Customization

### Styling
- `public/styles.css`: Custom CSS styles
- Tailwind classes: Utility-first styling
- Chart.js themes: Customizable chart appearances

### Configuration
- Update intervals: Modify in `dashboard.js`
- Chart data points: Adjust `maxDataPoints`
- Log entries: Configure `maxLogEntries`

## 📊 Monitoring Metrics

### System Metrics
- **CPU Usage**: Load average based percentage
- **Memory Usage**: Heap and system memory
- **Queue Load**: Active jobs in Redis queue
- **Storage Usage**: SQLite database sizes

### Pair Metrics
- **Record Count**: Total processed records per pair
- **Last Update**: Time since last data received
- **Error Count**: Number of errors per pair
- **Status**: Current operational status

### Performance Tracking
- **Throughput**: Records processed per second
- **Latency**: Data processing delays
- **Error Rate**: Error percentage over time
- **Uptime**: System availability metrics

## 🛠️ Troubleshooting

### Common Issues
1. **Dashboard Not Loading**: Check if port 3000 is available
2. **No Data Updates**: Verify WebSocket connection
3. **Pair Controls Not Working**: Check API endpoint accessibility
4. **Charts Not Displaying**: Verify Chart.js library loading

### Debug Mode
Enable debug logging in `.env`:
```
DEBUG_MODE=true
```

## 🔮 Future Enhancements

### Planned Features
- **Dark Mode**: Theme switching capability
- **Export Functions**: Data export to CSV/JSON
- **Alert System**: Email/SMS notifications
- **Historical Data**: Long-term trend analysis
- **Custom Dashboards**: User-configurable layouts
- **Multi-language**: Internationalization support

### Performance Optimizations
- **Data Compression**: Reduce bandwidth usage
- **Caching**: Improve response times
- **Lazy Loading**: Optimize initial load
- **Service Worker**: Offline capability
