# Binance Futures Tick Data Collector

A high-performance, scalable system for collecting tick data from Binance Futures WebSocket API.

## 🚀 Features

- **Real-time Data Collection**: Connects to Binance Futures WebSocket API for live tick data
- **Multi-Connection Support**: Distributes 100+ symbols across multiple WebSocket connections
- **Self-Healing Connections**: Automatic reconnection with exponential backoff
- **Redis Queue System**: Uses BullMQ for reliable data distribution
- **Multi-Core Processing**: Leverages all CPU cores with Node.js worker threads
- **SQLite Storage**: Efficient batch writes to daily database files
- **Dynamic Configuration**: Fetches configuration from HTTP API endpoint
- **Health Monitoring**: HTTP endpoints for system status and metrics
- **Comprehensive Logging**: Daily rotating log files with detailed error tracking

## 🏗️ System Architecture

```text
┌─────────────────┐    ┌──────────────┐    ┌────────────────┐
│  Tick Collector │───▶│ Redis Queue  │───▶│  Tick Workers  │
│ (WebSocket API) │    │ (BullMQ)     │    │ (Multi-core)   │
└─────────────────┘    └──────────────┘    └────────────────┘
                              │                    │
                              ▼                    ▼
                       ┌──────────────┐    ┌────────────────┐
                       │ Health       │    │ SQLite Storage │
                       │ Server       │    │ (Daily files)  │
                       └──────────────┘    └────────────────┘
```

## 🛠️ Technology Stack

- **Node.js** (v18+) - Runtime environment
- **Redis/BullMQ** - Queue management system
- **SQLite/better-sqlite3** - High-performance database storage
- **WebSocket** - Real-time data streaming
- **Express.js** - Health monitoring endpoints

## 🖥️ Hardware Requirements

- **Mac Studio M1 Max** (10 core CPU, 32 GB RAM recommended)
- **SSD Storage** (Minimum 256 GB, 500+ GB recommended)
- **Redis Server** running on localhost:6379

## 📦 Installation

1. Install dependencies:
```bash
npm install
```

2. Ensure Redis is running on localhost:6379

## ⚙️ Configuration

The system now fetches configuration dynamically from an HTTP API endpoint instead of using local configuration files.

### Configuration Endpoint

The system fetches configuration from: `http://localhost:3001/api/server/config`

Expected response format:
```json
{
  "id": 2,
  "dtCreated": "2025-07-28 17:52:08",
  "config": {
    "configID": "5j2d7z",
    "websocket": {
      "pairsPerConnection": 25,
      "healthCheckInterval": 10000,
      "reconnectInterval": 5000,
      "maxReconnectAttempts": 10
    },
    "pairs": [
      "VETUSDT",
      "PLUMEUSDT",
      "OPUSDT",
      "FLOWUSDT"
    ],
    "intervals": [
      "1m"
    ]
  }
}
```

### Configuration Mapping

- `config.websocket` → WebSocket connection settings
- `config.pairs` → Trading pairs to monitor
- `config.intervals[0]` → Kline interval (defaults to "1m")
- `config.configID` → Used as prefix for database file names

### Database File Naming

Database files are now named with the configID prefix:
`{configID}-ticks-{YYYY-MM-DD}.sqlite`

Example: `5j2d7z-ticks-2025-07-28.sqlite`

### Legacy Configuration Files

The old `config.json` and `symbols.json` files are no longer used but remain for reference.

## ▶️ Usage

Start the system:
```bash
npm start
```

For development with auto-restart:
```bash
npm run dev
```

## 🌐 Endpoints

- **Health Check**: http://localhost:3000/health
- **System Status**: http://localhost:3000/status
- **Prometheus Metrics**: http://localhost:3000/metrics

## 📊 Data Flow

1. **Tick Collector** connects to Binance Futures WebSocket API
2. **Incoming tick data** is parsed and validated
3. **Data is queued** in Redis for reliable distribution
4. **Tick Workers** consume from queue using all CPU cores
5. **Data is buffered** and written to SQLite in batches
6. **Daily database files** are automatically created (`ticks-YYYY-MM-DD.sqlite`)
7. **Health server** provides real-time monitoring endpoints

## 🔍 Monitoring & Observability

- **WebSocket Connection Status**: Tracks connection health and reconnection attempts
- **Redis Queue Metrics**: Monitors waiting, active, completed, and failed jobs
- **Processing Throughput**: Tracks ticks per second and system performance
- **Memory Usage**: Monitors heap usage and system resources
- **Daily Log Files**: Comprehensive logging in `logs/YYYY-MM-DD.log`

## 📁 File Structure

- `tick-collector.js`: Collects tick data from Binance WebSocket
- `tick-worker.js`: Processes queue data and writes to SQLite
- `queue.js`: Redis connection and queue management
- `sqlite-writer.js`: SQLite operations and batch insert functions
- `websocket-monitor.js`: WebSocket connection monitoring and self-healing
- `health-server.js`: Express.js health endpoint and status reporting
- `logger.js`: Logging functionality with daily rotating files
- `symbols.json`: Trading pair configuration
- `index.js`: Main application entry point

## 🛡️ Error Handling & Reliability

- **Automatic Reconnection**: WebSocket connections automatically reconnect on failure
- **Exponential Backoff**: Progressive delay between reconnection attempts
- **Queue Persistence**: Redis stores jobs to prevent data loss
- **Graceful Shutdown**: Proper cleanup of connections and data flushing
- **Error Recovery**: Failed jobs are retried with exponential backoff

## 📈 Performance Optimization

- **Batch Writing**: SQLite inserts are batched for efficiency
- **Buffer Management**: Per-symbol buffers with configurable flush intervals
- **Multi-Core Processing**: One worker thread per CPU core
- **Connection Pooling**: Efficient Redis connection management
- **Memory Efficient**: Streaming data processing with minimal memory footprint

## 📅 Daily Database Rotation

- Daily SQLite files: `ticks-YYYY-MM-DD.sqlite`
- Automatic file creation at midnight UTC
- Previous day files can be archived to S3 or other storage (optional)

## 🧪 Testing

Run tests:
```bash
npm test
```

## 🚀 Production Deployment

For production deployment, consider using PM2:
```bash
npm install -g pm2
pm2 start index.js --name binance-tick-collector
```

## 📞 Support

For issues and feature requests, please open an issue on GitHub.
