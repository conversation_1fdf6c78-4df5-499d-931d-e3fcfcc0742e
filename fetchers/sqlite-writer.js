const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const logger = require('./logger');
const configService = require('./config-service');
const dotenv = require('dotenv');
// Load environment variables from .env file
dotenv.config();

const debugMode = process.env.DEBUG_MODE === 'true';

class SQLiteWriter {
  constructor() {
    this.db = null;
    this.currentDate = this.getCurrentDate();
    this.configID = null;
    this.dbFile = null;
    this.buffers = new Map(); // Buffer for each symbol
    this.flushTimers = new Map(); // Timer for each symbol
    this.flushInterval = 1000; // 1 second
    this.batchSize = 100; // Flush when buffer reaches 100 records
    this.initialized = false;
    this.initializationPromise = null; // Track initialization promise to prevent multiple concurrent initializations
    this.klineInterval = '1m'; // Default interval
  }

  getCurrentDate() {
    const now = new Date();
    return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;
  }

  async getDBFilePath() {
    try {
      // Get configID from config service if not already set
      if (!this.configID) {
        let conf = await configService.getConfig();
        console.log('Config loaded:', conf);
        this.klineInterval = conf.websocket.klineInterval || '1m';
        this.configID = configService.getConfigID();
      }

      const dbDir = path.join(__dirname, 'data');
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }
      return path.join(dbDir, `${this.configID}-${this.klineInterval}-ticks-${this.currentDate}.sqlite`);
    } catch (error) {
      logger.error(`Failed to get config for database path: ${error.message}`);
      // Fallback to original naming if config service fails
      const dbDir = path.join(__dirname, 'data');
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }
      return path.join(dbDir, `${this.configID}-${this.klineInterval}ticks-${this.currentDate}.sqlite`);
    }
  }

  async checkDateRoll() {
    const currentDate = this.getCurrentDate();
    if (currentDate !== this.currentDate) {
      this.currentDate = currentDate;
      this.closeDatabase();
      this.dbFile = await this.getDBFilePath();
      // Reset initialized flag so database will be re-initialized with new file
      this.initialized = false;
      return true; // Indicate that date roll occurred
    }
    return false; // No date roll needed
  }

  async initDatabase() {
    // Don't call checkDateRoll here to avoid circular calls
    // checkDateRoll should be called before this method when needed

    try {
      if (!this.dbFile) {
        this.dbFile = await this.getDBFilePath();
      }

      this.db = new Database(this.dbFile);
      this.db.pragma('journal_mode = WAL');
      this.initialized = true;
      logger.info(`SQLite database initialized: ${this.dbFile}`);
    } catch (err) {
      logger.error(`Failed to initialize SQLite database: ${err.message}`);
      throw err;
    }
  }

  createTable(symbol) {
    // Ensure database is initialized before creating table
    if (!this.db) {
      logger.error('Database not initialized');
      return;
    }

    try {
      const tableName = symbol.toLowerCase();
      const createTableSQL = `
        CREATE TABLE IF NOT EXISTS "${tableName}" (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          event_type TEXT NOT NULL,
          event_time INTEGER NOT NULL,
          symbol TEXT NOT NULL,
          kline_start_time INTEGER NOT NULL,
          kline_end_time INTEGER NOT NULL,
          kline_symbol TEXT NOT NULL,
          kline_interval TEXT NOT NULL,
          kline_first_trade_id INTEGER NOT NULL,
          kline_last_trade_id INTEGER NOT NULL,
          kline_open_price REAL NOT NULL,
          kline_close_price REAL NOT NULL,
          kline_high_price REAL NOT NULL,
          kline_low_price REAL NOT NULL,
          kline_base_asset_volume REAL NOT NULL,
          kline_number_of_trades INTEGER NOT NULL,
          kline_is_closed BOOLEAN NOT NULL,
          kline_quote_asset_volume REAL NOT NULL,
          kline_taker_buy_base_asset_volume REAL NOT NULL,
          kline_taker_buy_quote_asset_volume REAL NOT NULL,
          timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
        );
      `;
      
      this.db.exec(createTableSQL);
      debugMode && logger.debug(`Table created/verified for symbol: ${symbol}`);
    } catch (err) {
      logger.error(`Failed to create table for symbol ${symbol}: ${err.message}`);
    }
  }

  async ensureInitialized() {
    if (!this.initialized) {
      // If initialization is in progress, wait for it to complete
      if (this.initializationPromise) {
        await this.initializationPromise;
        return;
      }
      
      // Start initialization and store the promise
      this.initializationPromise = this.initDatabase()
        .then(() => {
          this.initializationPromise = null;
        })
        .catch((error) => {
          this.initializationPromise = null;
          throw error;
        });
      
      await this.initializationPromise;
    }
  }

  async bufferTick(symbol, tick) {
    // Ensure database is initialized before buffering ticks
    await this.ensureInitialized();

    if (!this.buffers.has(symbol)) {
      this.buffers.set(symbol, []);
    }

    const buffer = this.buffers.get(symbol);
    buffer.push(tick);

    // Set up flush timer if not already set
    if (!this.flushTimers.has(symbol)) {
      this.flushTimers.set(symbol, setTimeout(() => {
        this.flushSymbol(symbol);
      }, this.flushInterval));
    }

    // Flush immediately if buffer is full
    if (buffer.length >= this.batchSize) {
      this.flushSymbol(symbol);
    }
  }

  flushSymbol(symbol) {
    if (!this.buffers.has(symbol) || this.buffers.get(symbol).length === 0) {
      return;
    }

    const buffer = this.buffers.get(symbol);
    this.writeBatch(symbol, buffer);
    
    // Clear buffer
    buffer.length = 0;
    
    // Clear timer
    if (this.flushTimers.has(symbol)) {
      clearTimeout(this.flushTimers.get(symbol));
      this.flushTimers.delete(symbol);
    }
  }

  writeBatch(symbol, ticks) {
    if (ticks.length === 0) return;

    // Check for date roll and re-initialize database if needed
    const dateRolled = this.checkDateRoll();
    if (dateRolled) {
      // If date rolled, we need to ensure database is initialized
      // This is a synchronous method, so we can't await ensureInitialized()
      // But the next call to createTable or database operations will trigger initialization
    }
    
    try {
      this.createTable(symbol);
      
      const tableName = symbol.toLowerCase();
      const insertSQL = `
        INSERT INTO "${tableName}" (
          event_type, event_time, symbol, kline_start_time, 
          kline_end_time, kline_symbol, kline_interval, kline_first_trade_id,
          kline_last_trade_id, kline_open_price, kline_close_price, kline_high_price,
          kline_low_price, kline_base_asset_volume, kline_number_of_trades, kline_is_closed,
          kline_quote_asset_volume, kline_taker_buy_base_asset_volume, kline_taker_buy_quote_asset_volume
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      const stmt = this.db.prepare(insertSQL);
      
      // Begin transaction
      const transaction = this.db.transaction((ticks) => {
        for (const tick of ticks) {
          // Ensure all values are of correct types with proper validation for kline data
          const eventType = String(tick.eventType || 'kline');
          const eventTime = Number(tick.eventTime) || 0;
          const symbol = String(tick.symbol || '');
          
          // Kline data
          const kline = tick.kline || {};
          const klineStartTime = Number(kline.startTime) || 0;
          const klineEndTime = Number(kline.endTime) || 0;
          const klineSymbol = String(kline.symbol || '');
          const klineInterval = String(kline.interval || '');
          const klineFirstTradeId = Number(kline.firstTradeId) || 0;
          const klineLastTradeId = Number(kline.lastTradeId) || 0;
          const klineOpenPrice = Number(kline.openPrice) || 0;
          const klineClosePrice = Number(kline.closePrice) || 0;
          const klineHighPrice = Number(kline.highPrice) || 0;
          const klineLowPrice = Number(kline.lowPrice) || 0;
          const klineBaseAssetVolume = Number(kline.baseAssetVolume) || 0;
          const klineNumberOfTrades = Number(kline.numberOfTrades) || 0;
          const klineIsClosed = Boolean(kline.isClosed) ? 1 : 0;
          const klineQuoteAssetVolume = Number(kline.quoteAssetVolume) || 0;
          const klineTakerBuyBaseAssetVolume = Number(kline.takerBuyBaseAssetVolume) || 0;
          const klineTakerBuyQuoteAssetVolume = Number(kline.takerBuyQuoteAssetVolume) || 0;
          
          const values = [
            eventType, eventTime, symbol, klineStartTime,
            klineEndTime, klineSymbol, klineInterval, klineFirstTradeId,
            klineLastTradeId, klineOpenPrice, klineClosePrice, klineHighPrice,
            klineLowPrice, klineBaseAssetVolume, klineNumberOfTrades, klineIsClosed,
            klineQuoteAssetVolume, klineTakerBuyBaseAssetVolume, klineTakerBuyQuoteAssetVolume
          ];
          
          stmt.run(...values);
        }
      });
      
      // Execute transaction
      transaction(ticks);
      
      debugMode && logger.debug(`Inserted ${ticks.length} klines for symbol ${symbol}`);
    } catch (err) {
      logger.error(`Failed to write batch for symbol ${symbol}: ${err.message}`);
    }
  }

  flushAll() {
    for (const symbol of this.buffers.keys()) {
      this.flushSymbol(symbol);
    }
  }

  closeDatabase() {
    if (this.db) {
      try {
        this.flushAll();
        this.db.close();
        logger.info(`SQLite database closed: ${this.dbFile}`);
      } catch (err) {
        logger.error(`Error closing database: ${err.message}`);
      } finally {
        this.db = null;
      }
    }
  }
}

module.exports = SQLiteWriter;
