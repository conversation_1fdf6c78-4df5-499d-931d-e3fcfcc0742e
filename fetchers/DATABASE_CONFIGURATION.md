# Veritabanı Konfigürasyonu

Bu uygulama iki farklı veritabanı seçeneğini destekler:

1. **SQLite** (Varsayılan)
2. **TimescaleDB/PostgreSQL**

## Konfigürasyon

Veritabanı seçimi `config.json` dosyası ile yapılır:

### SQLite Kullanımı
```json
{
  "databaseServer": "sqlite"
}
```

### TimescaleDB/PostgreSQL Kullanımı
```json
{
  "databaseServer": "timescaledb"
}
```

## SQLite Özellikleri

- **Dosya tabanlı**: Günlük SQLite dosyaları oluşturur
- **Dosya formatı**: `{configID}-{interval}-ticks-{YYYY-MM-DD}.sqlite`
- **Konum**: `./data/` klasörü
- **Batch işleme**: Evet (100 kayıt veya 1 saniye)
- **Günlük dosya rotasyonu**: Evet

## TimescaleDB/PostgreSQL Özellikleri

- **Sunucu tabanlı**: PostgreSQL veritabanına bağlanır
- **Tablo formatı**: Her sembol için ayrı tablo (`btcusdt`, `ethusdt`, vb.)
- **Hypertable**: TimescaleDB extension varsa otomatik hypertable oluşturur
- **Batch işleme**: Evet (optimized batch insert)
- **Günlük dosya rotasyonu**: Hayır (gerek yok)

## TimescaleDB Bağlantı Ayarları

TimescaleDB kullanımı için `timescaledb-writer.js` dosyasındaki bağlantı ayarlarını düzenleyin:

```javascript
const DB_CONFIG = {
  host: "localhost",
  port: 5432,
  database: "postgres",
  user: "tanersubasi",
  password: ""
};
```

## Performans Karşılaştırması

### SQLite
- ✅ Kurulum gerektirmez
- ✅ Günlük dosya rotasyonu
- ✅ Backup kolay
- ❌ Eşzamanlı yazma sınırlı
- ❌ Büyük veri setlerinde yavaş

### TimescaleDB
- ✅ Yüksek performans
- ✅ Eşzamanlı işlem desteği
- ✅ Zaman serisi optimizasyonu
- ✅ Büyük veri setleri için ideal
- ❌ PostgreSQL kurulumu gerekli
- ❌ Daha karmaşık setup

## Geçiş

Veritabanı türünü değiştirmek için:

1. `config.json` dosyasını düzenleyin
2. Uygulamayı yeniden başlatın
3. Yeni veriler seçilen veritabanına yazılacaktır

**Not**: Mevcut veriler otomatik olarak taşınmaz. Gerekirse manuel migration yapılmalıdır.

## Tablo Yapısı

Her iki veritabanında da aynı tablo yapısı kullanılır:

```sql
CREATE TABLE symbol_name (
  id INTEGER/SERIAL PRIMARY KEY,
  event_type TEXT NOT NULL,
  event_time BIGINT NOT NULL,
  symbol TEXT NOT NULL,
  kline_start_time BIGINT NOT NULL,
  kline_end_time BIGINT NOT NULL,
  kline_symbol TEXT NOT NULL,
  kline_interval TEXT NOT NULL,
  kline_first_trade_id BIGINT NOT NULL,
  kline_last_trade_id BIGINT NOT NULL,
  kline_open_price DECIMAL NOT NULL,
  kline_close_price DECIMAL NOT NULL,
  kline_high_price DECIMAL NOT NULL,
  kline_low_price DECIMAL NOT NULL,
  kline_base_asset_volume DECIMAL NOT NULL,
  kline_number_of_trades INTEGER NOT NULL,
  kline_is_closed BOOLEAN NOT NULL,
  kline_quote_asset_volume DECIMAL NOT NULL,
  kline_taker_buy_base_asset_volume DECIMAL NOT NULL,
  kline_taker_buy_quote_asset_volume DECIMAL NOT NULL,
  timestamp TIMESTAMPTZ DEFAULT NOW()
);
```

## Troubleshooting

### TimescaleDB Bağlantı Hatası
- PostgreSQL servisinin çalıştığından emin olun
- Bağlantı bilgilerini kontrol edin
- Kullanıcı izinlerini kontrol edin

### SQLite Dosya Hatası
- `./data/` klasörünün yazma izni olduğundan emin olun
- Disk alanının yeterli olduğunu kontrol edin
