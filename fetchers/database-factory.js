const fs = require('fs');
const path = require('path');
const logger = require('./logger');

class DatabaseFactory {
  constructor() {
    this.configPath = path.join(__dirname, 'config.json');
    this.writer = null;
  }

  loadConfig() {
    try {
      const configData = fs.readFileSync(this.configPath, 'utf8');
      return JSON.parse(configData);
    } catch (error) {
      logger.error(`Failed to load config.json: ${error.message}`);
      // Default to sqlite if config can't be loaded
      return { databaseServer: 'sqlite' };
    }
  }

  createWriter() {
    if (this.writer) {
      return this.writer;
    }

    const config = this.loadConfig();
    const databaseServer = config.databaseServer || 'sqlite';

    logger.info(`Creating database writer for: ${databaseServer}`);

    switch (databaseServer.toLowerCase()) {
      case 'timescaledb':
      case 'postgresql':
        const TimescaleDBWriter = require('./timescaledb-writer');
        this.writer = new TimescaleDBWriter();
        logger.info('Using TimescaleDB/PostgreSQL writer');
        break;
      
      case 'sqlite':
      default:
        const SQLiteWriter = require('./sqlite-writer');
        this.writer = new SQLiteWriter();
        logger.info('Using SQLite writer');
        break;
    }

    return this.writer;
  }

  getWriter() {
    return this.createWriter();
  }

  async closeWriter() {
    if (this.writer) {
      if (typeof this.writer.closeDatabase === 'function') {
        await this.writer.closeDatabase();
      }
      this.writer = null;
    }
  }
}

// Export singleton instance
module.exports = new DatabaseFactory();
