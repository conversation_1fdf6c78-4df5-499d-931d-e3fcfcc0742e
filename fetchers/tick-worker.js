const { Worker, connection } = require('./queue');
const SQLiteWriter = require('./sqlite-writer');
const logger = require('./logger');
const os = require('os');
const dotenv = require('dotenv');
// Load environment variables from .env file
dotenv.config();
const debugMode = process.env.DEBUG_MODE === 'true';

class TickWorker {
  constructor() {
    this.workers = [];
    this.sqliteWriter = new SQLiteWriter();
    this.workerCount = os.cpus().length;
    this.processedCount = 0;
    this.startTime = Date.now();
  }

  start() {
    logger.info(`Starting ${this.workerCount} tick workers...`);
    
    // Create workers for each CPU core
    for (let i = 0; i < this.workerCount; i++) {
      const worker = new Worker('tickQueue', async (job) => {
        try {
          const kline = job.data;

          // Write kline to SQLite
          await this.sqliteWriter.bufferTick(kline.symbol, kline);

          // Log progress every 1000 klines
          this.processedCount++;
          if (this.processedCount % 1000 === 0) {
            const rate = this.processedCount / ((Date.now() - this.startTime) / 1000);
            debugMode && logger.info(`Processed ${this.processedCount} klines. Rate: ${rate.toFixed(2)} klines/sec`);
          }

          return { success: true };
        } catch (error) {
          logger.error(`Error processing kline: ${error.message}`);
          throw error;
        }
      }, {
        connection,
        concurrency: 10 // Process up to 10 jobs concurrently per worker
      });
      
      worker.on('completed', (job) => {
        debugMode && logger.debug(`Worker completed job ${job.id}`);
      });
      
      worker.on('failed', (job, err) => {
        logger.error(`Worker failed job ${job.id}: ${err.message}`);
      });
      
      worker.on('error', (err) => {
        logger.error(`Worker error: ${err.message}`);
      });
      
      this.workers.push(worker);
    }
    
    logger.info(`Started ${this.workers.length} tick workers`);
  }

  getStatus() {
    return {
      workerCount: this.workers.length,
      processedCount: this.processedCount,
      uptime: Date.now() - this.startTime,
      bufferSizes: Array.from(this.sqliteWriter.buffers.entries()).map(([symbol, buffer]) => ({
        symbol,
        size: buffer.length
      }))
    };
  }

  async close() {
    logger.info('Closing tick workers...');
    
    // Close all workers
    for (const worker of this.workers) {
      await worker.close();
    }
    
    // Flush all buffered data
    this.sqliteWriter.flushAll();
    
    // Close database
    this.sqliteWriter.closeDatabase();
    
    logger.info('All tick workers closed');
  }
}

// If this file is run directly, start the workers
if (require.main === module) {
  const tickWorker = new TickWorker();
  
  // Handle graceful shutdown
  const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];
  signals.forEach(signal => {
    process.on(signal, async () => {
      logger.info(`Received ${signal}, shutting down gracefully...`);
      await tickWorker.close();
      process.exit(0);
    });
  });
  
  tickWorker.start();
}

module.exports = TickWorker;
