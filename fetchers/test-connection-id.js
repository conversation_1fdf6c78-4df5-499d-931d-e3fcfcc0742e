const { getTradingPairData } = require('./db-query-utils');
const system = require('./index');
const configService = require('./config-service');

async function test() {
  // First, we need to initialize the system to have a tickCollector instance
  try {
    await system.start();
    
    // Debug: Check if tickCollector exists and has symbols
    console.log('TickCollector exists:', !!system.tickCollector);
    if (system.tickCollector) {
      console.log('Symbols map size:', system.tickCollector.symbols.size);
      console.log('Symbols map keys:', Array.from(system.tickCollector.symbols.keys()));
      
      // Test the getConnectionIdForSymbol method directly
      const testSymbol = 'adausdt';
      const connectionId = system.tickCollector.getConnectionIdForSymbol(testSymbol);
      console.log('Connection ID for', testSymbol, ':', connectionId);
    }
    
    // Get the config to see what symbols we have
    await configService.getConfig();
    const symbolsConfig = configService.getSymbolsConfig();
    console.log('Available symbols:', symbolsConfig);
    
    // Test with the first symbol from our config
    if (symbolsConfig.connections && symbolsConfig.connections.length > 0 && 
        symbolsConfig.connections[0].pairs && symbolsConfig.connections[0].pairs.length > 0) {
      const testSymbol = symbolsConfig.connections[0].pairs[0];
      console.log('Testing with symbol:', testSymbol);
      
      const testData = await getTradingPairData(testSymbol);
      console.log('Test data for', testSymbol, ':', testData);
      
      // Check specifically if connectionId is present
      if ('connectionId' in testData) {
        console.log('SUCCESS: connectionId is included in the response');
        console.log('Connection ID:', testData.connectionId);
      } else {
        console.log('ERROR: connectionId is missing from the response');
      }
    } else {
      console.log('No symbols found in config');
    }
  } catch (error) {
    console.error('Error in test:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    // Don't shutdown the system as it might affect other processes
    // system.shutdown();
  }
}

test();
